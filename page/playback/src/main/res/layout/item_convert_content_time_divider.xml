<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <!--使用要根据实际的XML来进行适配-->
        <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
            android:id="@+id/animator_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:background="@drawable/background_convert_speaker"
            app:max_padding_end="@dimen/dp6">

            <LinearLayout
                android:id="@+id/ll_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

               <!-- <ImageView
                    android:id="@+id/iv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp8"
                    android:src="@drawable/ic_circle1" />-->

                <TextView
                    android:id="@+id/tv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:paddingVertical="@dimen/dp4"
                    android:paddingHorizontal="@dimen/dp11"
                    android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                    android:textColor="@color/coui_color_label_primary"
                    android:textFontWeight="500"
                    android:textSize="@dimen/sp10"
                    android:textStyle="bold"
                    tools:text="@string/convert_speaker" />

            </LinearLayout>
        </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

        <TextView
            android:id="@+id/start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription=""
            android:ellipsize="end"
            android:lines="1"
            android:text="00:12"
            android:layout_marginStart="@dimen/dp4"
            android:textColor="@color/coui_color_label_secondary"
            android:textAppearance="@style/couiTextBodyXS"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum" />
    </LinearLayout>

</LinearLayout>