/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - AISummaryBuryingUtil.kt
 ** Description: AISummaryBuryingUtil
 ** Version: 1.0
 ** Date : 2025/6/14
 ** Author: tianyanyue
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** tianyanyue    2025/6/14    1.0    create
 ****************************************************************/
package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction.addNewCommonUserAction

object AISummaryBuryingUtil {
    private const val TAG = "AISummaryBuryingUtil"
    private const val EVENT_GROUP_SHARE = "share"

    /*分享的埋点*/
    private const val EVENT_ID_SHARE = "event_share"

    /*录音编号*/
    private const val KEY_AII_SHARE_UNI_FILE_ID: String = "uni_file_id"

    /*用户点击分享的类型*/
    private const val KEY_AI_SHARE_TYPE: String = "type"
    const val VALUE_SHARE_LINK = "0"
    const val VALUE_SHARE_AUDIO = "1"
    const val VALUE_SHARE_DOCUMENT = "2"
    const val VALUE_SHARE_SUMMARY = "3"
    const val VALUE_SHARE_MIND_MAP = "4"

    //分享格式
    private const val KEY_AI_SHARE_FORMAT: String = "format"
    const val VALUE_FORMAT_WORD: String = "1"
    const val VALUE_FORMAT_PDF: String = "2"
    const val VALUE_FORMAT_TXT: String = "3"
    const val VALUE_FORMAT_NOTE: String = "4"
    const val VALUE_FORMAT_XMIND: String = "5"
    const val VALUE_FORMAT_IMAGE: String = "6"

    /*录音多智能功能生成后导出、复制*/
    private const val EVENT_AI_RECORDING_INTELL_PAGE_SHARE: String = "recording_intell_page_share"

    /*录音编号*/
    private const val KEY_AI_UNI_FILE_ID: String = "uni_file_id"

    /*操作功能类别*/
    private const val KEY_AI_TYPE: String = "type"

    /*录音摘要*/
    const val VALUE_TYPE_SUMMARY: String = "1"

    /*转文本*/
    const val VALUE_TYPE_TEXT: String = "2"

    /*思维导图*/
    const val VALUE_TYPE_MIND_MAP: String = "3"

    /*操作*/
    private const val KEY_AI_ACTION: String = "action"

    const val VALUE_ACTION_EXPORT: String = "1"

    const val VALUE_ACTION_COPY: String = "2"

    /*导出类型*/
    private const val KEY_AI_CHANNEL: String = "channel"
    const val VALUE_EXPORT_NOTE: String = "1"
    const val VALUE_EXPORT_DOCUMENT: String = "2"
    const val VALUE_EXPORT_OTHER: String = "3"

    /*导出格式*/
    private const val KEY_AI_FORMAT: String = "format"


    /*导出结果*/
    private const val KEY_AI_RETURN: String = "return"
    const val VALUE_RETURN_SUCCESS: Int = 1
    const val VALUE_RETURN_FAILURE: Int = 0
    const val VALUE_RETURN_CANCEL: Int = 2

    /*操作原因*/
    private const val KEY_AI_FAIL_REASON: String = "fail_reason"

    /*生成文件失败*/
    const val VALUE_FILE_GENERATION_FAILED = "1"

    /*便签安装失败*/
    const val VALUE_STICKER_INSTALLATION_FAILED = "2"

    /*便签安装过程中取消*/
    const val VALUE_STICKER_INSTALLATION_CANCELLED = "3"

    /*其他错误*/
    const val VALUE_OTHER_ERROR = "4"

    /*录音摘要场景*/
    private const val EVENT_SUMMARY_SCENE = "summary_scene"

    /* 是否第一次生成: 0 -> 不是, 1 -> 是 */
    const val KEY_IS_FIRST: String = "if_first"
    const val VALUE_IS_NOT_FIRST: Int = 0
    const val VALUE_IS_FIRST: Int = 1

    /* 1 -> 会议纪要, 2 -> 知识笔记, 3-> 访谈记录, 4 -> 通话摘要, 5 -> 简单摘要, 6 -> 详细摘要*/
    const val VALUE_TYPE_MEETING = "1"
    const val VALUE_TYPE_NOTE = "2"
    const val VALUE_TYPE_INTERVIEW = "3"
    const val VALUE_TYPE_CALL = "4"
    const val VALUE_TYPE_SIMPLE = "5"
    const val VALUE_TYPE_DETAIL = "6"
    /**
     * 录音分享摘要埋点
     * fileId:录音编号
     * type: 分享链接 ->0 分享音频 -> 1 分享原文档 -> 2  分享摘要 -> 3 分享思维导图 -> 4
     * format: 分享格式  word -> 1 pdf -> 2 txt -> 3  xmind ->4  图片 5
     */
    @JvmStatic
    fun addShareSummaryEvent(fileId: String, type: String, format: String) {
        val info = HashMap<String, String>()
        info[KEY_AII_SHARE_UNI_FILE_ID] = fileId
        info[KEY_AI_SHARE_TYPE] = type
        info[KEY_AI_SHARE_FORMAT] = format
        DebugUtil.i(TAG, "addShareSummaryEvent info $info")
        addNewCommonUserAction(
            BaseApplication.getAppContext(),
            EVENT_GROUP_SHARE,
            EVENT_ID_SHARE,
            info,
            false
        )
    }

    data class ShareEventInfo(
        val fileId: String,
        val type: String,
        val action: String,
        val channel: String,
        val format: String,
        val outcome: Int,
        val failReason: String
    )

    /**
     * 录音多智能功能生成后导出、复制
     * fileId:录音编号
     * type:  摘要 -> 1 文本 -> 2 思维导图 -> 3
     * action:  导出 -> 1 复制 -> 2
     * channel:分享哪个ap
     * format: 分享格式 word -> 1 pdf -> 2 txt -> 3 便签 -> 4  xmind ->5 图片 6
     * outcome: 失败 -> 0 成功 -> 1
     * failReason: 失败原因
     *
     * 只有导出上报,复制不上报
     * https://odocs.myoas.com/sheets/m4kMLxKbMEHl2vqD/MODOC?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
     */
    @JvmStatic
    fun addRecordShareEvent(info: ShareEventInfo) {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_AI_UNI_FILE_ID] = info.fileId
        eventInfo[KEY_AI_TYPE] = info.type
        eventInfo[KEY_AI_ACTION] = info.action
        eventInfo[KEY_AI_CHANNEL] = info.channel
        eventInfo[KEY_AI_FORMAT] = info.format
        eventInfo[KEY_AI_RETURN] = info.outcome.toString()
        eventInfo[KEY_AI_FAIL_REASON] = info.failReason
        addNewCommonUserAction(
            BaseApplication.getAppContext(),
            EVENT_AI_RECORDING_INTELL_PAGE_SHARE,
            EVENT_AI_RECORDING_INTELL_PAGE_SHARE,
            eventInfo,
            false
        )
        DebugUtil.i(TAG, "addRecordShareEvent info $eventInfo")
    }


    /**
     * 录音摘要场景
     * file_id:录音编号
     * isFirst： 是否第一次生成: 0 -> 不是, 1 -> 是
     * type： 摘要生成类型：
     * 1 -> 会议纪要 2 -> 知识笔记 3 -> 访谈记录
     * 4 -> 通话摘要 5 -> 简单摘要 6 -> 详细摘要
     */
    @JvmStatic
    fun addRecordSummaryScene(fileId: String, ifFirst: Int, type: String) {
        val info = HashMap<String, String>()
        info[KEY_AI_UNI_FILE_ID] = fileId
        info[KEY_IS_FIRST] = ifFirst.toString()
        info[KEY_AI_TYPE] = type
        addNewCommonUserAction(
            BaseApplication.getAppContext(),
            EVENT_AI_RECORDING_INTELL_PAGE_SHARE,
            EVENT_SUMMARY_SCENE,
            info,
            false
        )
        DebugUtil.i(TAG, "addRecordSummaryScene info $info")
    }
}