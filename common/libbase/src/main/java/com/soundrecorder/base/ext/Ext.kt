package com.soundrecorder.base.ext

import android.app.Activity
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.media.AudioManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.fragment.app.commitNow
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import java.io.File
import java.lang.reflect.Method

const val TAG = "Ext"

fun AppCompatActivity.replaceFragment(@IdRes containerId: Int, fragment: Class<out Fragment>, args: Bundle? = null) {
    try {
        supportFragmentManager.beginTransaction().replace(containerId, fragment, args).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}

fun AppCompatActivity.replaceFragment(@IdRes containerId: Int, fragment: Fragment?) {
    if (fragment == null) {
        DebugUtil.d("replaceFragment", "replaceFragment is null return")
        return
    }
    try {
        supportFragmentManager.beginTransaction().replace(containerId, fragment).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}


fun AppCompatActivity.replaceFragmentByTag(@IdRes containerId: Int, fragment: Fragment?, tag: Any?) {
    if (fragment == null) {
        DebugUtil.d("replaceFragment", "replaceFragment is null return")
        return
    }
    try {
        supportFragmentManager.beginTransaction().replace(containerId, fragment, tag?.toString()).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}

fun <T : Fragment> AppCompatActivity.findFragment(tag: Any?): T? {
    if (tag == null) {
        return null
    }
    try {
        return supportFragmentManager.findFragmentByTag(tag.toString()) as? T
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
    return null
}

fun AppCompatActivity.hideFragment(fragment: Fragment?, allowStateLoss: Boolean = false) {
    if (fragment == null) {
        return
    }
    runCatching {
        if (!fragment.isHidden) {
            supportFragmentManager.commit(allowStateLoss) {
                hide(fragment)
            }
        }
    }.onFailure {
        DebugUtil.e("hideFragment", "hideFragment error.", it)
    }
}

fun AppCompatActivity.showFragment(fragment: Fragment?, allowStateLoss: Boolean = false) {
    if (fragment == null) {
        return
    }
    runCatching {
        if (fragment.isHidden) {
            supportFragmentManager.commit(allowStateLoss) {
                show(fragment)
            }
        }
    }.onFailure {
        DebugUtil.e("showFragment", "showFragment error.", it)
    }
}

fun AppCompatActivity.getFragmentInstance(fragmentClass: Class<out Fragment>): Fragment? {
    var fragment: Fragment? = null
    try {
        fragment = supportFragmentManager.fragmentFactory.instantiate(classLoader, fragmentClass.name)
    } catch (e: Exception) {
        DebugUtil.e("getFragmentInstance", "instantiate error.", e)
    }
    return fragment
}

fun AppCompatActivity.removeFragment(fragment: Fragment?) {
    if (fragment == null) {
        return
    }
    try {
        supportFragmentManager.beginTransaction().remove(fragment).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("removeFragment", "remove error.", e)
    }
}

fun AppCompatActivity.removeFragmentCustomAnimations(fragment: Fragment?) {
    if (fragment == null) {
        return
    }
    supportFragmentManager.beginTransaction()
        .setCustomAnimations(com.support.appcompat.R.anim.coui_close_slide_enter,
            com.support.appcompat.R.anim.coui_close_slide_exit)
        .remove(fragment)
        .commitAllowingStateLoss()
}

fun AppCompatActivity.removeFragment(fragmentClass: Class<out Fragment>) {
    try {
        val fragmentInstance = getFragmentInstance(fragmentClass)
        fragmentInstance?.let {
            supportFragmentManager.beginTransaction().remove(fragmentInstance).commitAllowingStateLoss()
        }
    } catch (e: Exception) {
        DebugUtil.e("removeFragment", "remove error.", e)
    }
}

fun Fragment.replaceFragment(@IdRes containerId: Int, fragment: Class<out Fragment>, args: Bundle? = null) {
    try {
        childFragmentManager.beginTransaction().replace(containerId, fragment, args).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}

fun Fragment.replaceFragment(@IdRes containerId: Int, fragment: Fragment?) {
    if (fragment == null) {
        DebugUtil.d("replaceFragment", "replaceFragment is null return")
        return
    }
    try {
        childFragmentManager.beginTransaction().replace(containerId, fragment).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}

fun Fragment.replaceFragmentByTag(@IdRes containerId: Int, fragment: Fragment?, tag: Any?) {
    if (fragment == null) {
        DebugUtil.d("replaceFragment", "replaceFragment is null return")
        return
    }
    try {
        childFragmentManager.beginTransaction().replace(containerId, fragment, tag?.toString()).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
}

fun <T : Fragment> Fragment.findFragment(tag: Any?): T? {
    if (tag == null) {
        return null
    }
    try {
        return childFragmentManager.findFragmentByTag(tag.toString()) as? T
    } catch (e: Exception) {
        DebugUtil.e("replaceFragment", "replace error.", e)
    }
    return null
}

fun Fragment.removeFragment(fragment: Fragment?) {
    if (fragment == null) return
    try {
        childFragmentManager.beginTransaction().remove(fragment).commitAllowingStateLoss()
    } catch (e: Exception) {
        DebugUtil.e("removeFragment", "remove error.", e)
    }
}

/**
 * 1. When the recording interface exits abnormally,
 * if the ringing mode is set, it will return to normal mode.
 * 2. If the user has changed the ring mode during recording,
 * the mode will not be restored.
 * */
fun Activity.restoreRingMode() {
    DebugUtil.i(TAG, "sNeedToNormalRingMode = ${BaseApplication.sNeedToNormalRingMode} , isFinishing = $isFinishing.")
    if (BaseApplication.sNeedToNormalRingMode && isFinishing) {
        val audioManager: AudioManager? = getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        DebugUtil.i(TAG, "current ring mode ${audioManager?.ringerMode} .")
        if (audioManager?.ringerMode == AudioManager.RINGER_MODE_VIBRATE) {
            audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
        }
        BaseApplication.sNeedToNormalRingMode = false
    }
}

/**
 * After a string having trailing whitespace removed,
 * Splits this char sequence to a list of strings around occurrences of the specified [delimiter].
 */
fun String.splitTrimEnd(delimiter: String): List<String>? {
    return if (TextUtils.isEmpty(this)) {
        null
    } else {
        trimEnd().split(delimiter)
    }
}


/**
 * After a string having trailing whitespace removed,
 * Splits this char sequence to a list of strings around occurrences of the specified [delimiter].
 */
fun String.splitOddTrimEnd(): List<String>? {
    return if (TextUtils.isEmpty(this)) {
        null
    } else {
        val newString = trimEnd()
        val listString = ArrayList<String>()
        for (i in newString.indices step (2)) {
            listString.add(newString.get(i).toString())
        }
        listString
    }
}

/**
 * Get the title part of the path or name string
 */
fun String?.title(): String? {
    var title = this
    val lastSeparator = this?.lastIndexOf(File.separator) ?: -1
    if (lastSeparator > -1) {
        val subIndex = lastSeparator + 1
        title = if (subIndex < this?.length ?: 0) {
            this?.substring(subIndex)
        } else {
            ""
        }
    }
    val lastDot = title?.lastIndexOf(".") ?: -1
    if (lastDot > -1) {
        title = title?.substring(0, lastDot)
    }
    return title
}

fun String?.displayName(): String? {
    var displayName = this
    val lastSeparator = this?.lastIndexOf(File.separator) ?: -1
    if (lastSeparator > -1) {
        val subIndex = lastSeparator + 1
        displayName = if (subIndex < this?.length ?: 0) {
            this?.substring(subIndex)
        } else {
            ""
        }
    }
    return displayName
}

/**
 * Get the suffix part of the path or display name string
 */
fun String?.suffix(): String? {
    var suffix: String? = ""
    val lastDot = this?.lastIndexOf(".") ?: -1
    if (lastDot > -1) {
        suffix = this?.substring(lastDot)
    }
    return suffix
}


fun allNotNull(vararg args: Any?): Boolean {
    return args.filterNotNull().size == args.size
}

fun Long?.durationInMsFormatTimeExclusive(leastOneSecond: Boolean = false): String {
    val duration = (this ?: 0)
    return TimeUtils.getFormatTimeExclusiveMill(duration, leastOneSecond)
}

fun Long?.currentInMsFormatTimeExclusive(duration: Long?): String {
    val currentTime = (this ?: 0)
    val durationTime = (duration ?: 0)
    /*注意：帧率目前写死的100，但是实际上播放是70，首页快捷是100，由于1s内音频为小概率事件，所以暂定写死100*/
    return TimeUtils.getFormatTimeExclusiveMill(
        currentTime,
        (durationTime > 0) && (durationTime < TimeUtils.TIME_ONE_SECOND) && (durationTime - currentTime < TimeUtils.TIME_MS_100)
    )
}

fun Long?.durationHasHourFormatTimeExclusive(leastOneSecond: Boolean = false): String {
    val duration = (this ?: 0)
    return TimeUtils.getFormatTimeExclusiveMillLeastHour(duration, leastOneSecond)
}

fun Long?.currentHasHourFormatTimeExclusive(duration: Long?): String {
    val currentTime = (this ?: 0)
    val durationTime = (duration ?: 0)
    /*注意：帧率目前写死的100，但是实际上播放是70，首页快捷是100，由于1s内音频为小概率事件，所以暂定写死100*/
    return TimeUtils.getFormatTimeExclusiveMillLeastHour(
        currentTime,
        (durationTime > 0) && (durationTime < TimeUtils.TIME_ONE_SECOND) && (durationTime - currentTime < TimeUtils.TIME_MS_100)
    )
}

fun Long?.currentInMsFormatTimeTalkBack(context: Context, duration: Long?): String {
    val currentTime = (this ?: 0)
    val durationTime = (duration ?: 0)
    /*注意：帧率目前写死的100，但是实际上播放是70，首页快捷是100，由于1s内音频为小概率事件，所以暂定写死100*/
    return TimeUtils.getDurationHint(context,
        currentTime,
        (durationTime > 0) && (durationTime < TimeUtils.TIME_ONE_SECOND) && (durationTime - currentTime < TimeUtils.TIME_MS_100)
    )
}

fun Dialog.setWidth(dialogWidth: Int) {
    val dialog = this

    if (dialogWidth < 0 || dialogWidth > 360) {
        return
    }
    val resources = BaseApplication.getAppContext().resources
    val dm = resources.displayMetrics
    var ratio = 0.888888
    if (dialogWidth != 0) {
        ratio = (dialogWidth + 32).toDouble() / 360.0
    }
    val toInt = (dm.widthPixels * ratio).toInt()
    dialog.window?.setLayout(toInt, ViewGroup.LayoutParams.WRAP_CONTENT)
}

fun <T> MutableLiveData<T>.postValueSafe(t: T?) {
    if (Looper.getMainLooper()?.thread?.id == Thread.currentThread().id) {
        value = t
    } else {
        postValue(t)
    }
}

fun MutableLiveData<Long>.getValueWithDefault(): Long = value ?: 0

fun Fragment.isInMultiWindowMode(): Boolean {
    activity?.apply {
        if (isFinishing || isDestroyed) {
            return false
        }
        return isInMultiWindowMode
    }
    return false
}

fun isLandscape(): Boolean {
    return BaseApplication.getApplication().resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
}

fun Intent.getStringExtraSecure(name: String): String? {
    return try {
        getStringExtra(name)
    } catch (_: Exception) {
        null
    }
}

fun Context.registerReceiverCompat(receiver: BroadcastReceiver?, intentFilter: IntentFilter?, flags: Int = Context.RECEIVER_EXPORTED) {
    registerReceiverCompat(receiver, intentFilter, null, null, flags)
}
fun Context.registerReceiverCompat(
    receiver: BroadcastReceiver?,
    intentFilter: IntentFilter?,
    permission: String? = null,
    handler: Handler? = null,
    flags: Int = Context.RECEIVER_EXPORTED
) {
    if (BaseUtil.isAndroidTOrLater) {
        registerReceiver(receiver, intentFilter, permission, handler, flags)
    } else {
        registerReceiver(receiver, intentFilter, permission, handler)
    }
}

fun AlertDialog?.dismissWhenShowing() {
    this ?: return
    if (isShowing) {
        dismiss()
    }
}

/**
 * 提供三方应用以及系统内部应用辨别是否为flexible浮窗或者分屏窗口的接口
 * FLEXIBLE_WINDOW_INVALID_MODE = -1   非分屏也非浮窗
 * FLEXIBLE_WINDOW_FREEFORM_MODE = 1    浮窗态
 * FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE = 2  分屏态
 */
const val FLEXIBLE_WINDOW_INVALID_MODE = -1
const val FLEXIBLE_WINDOW_FREEFORM_MODE = 1
const val FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE = 2

fun getFlexibleWindowState(activity: Activity?): Int {
    var state = FLEXIBLE_WINDOW_INVALID_MODE
    if (activity == null) {
        FLEXIBLE_WINDOW_INVALID_MODE
    } else {
        try {
            val classz = Class.forName("com.oplus.flexiblewindow.FlexibleWindowManager")
            val instanceMethod: Method = classz.getMethod("getInstance")
            val instance: Any = instanceMethod.invoke(null)
            val method: Method = classz.getMethod("getFlexibleWindowState", Activity::class.java)
            val ret: Any = method.invoke(instance, activity)
            state = ret as Int
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getFlexibleWindowState error:${e.message}")
        }
    }
    return state
}

fun isFlexibleWindow(activity: Activity?): Boolean {
    return getFlexibleWindowState(activity) == FLEXIBLE_WINDOW_FREEFORM_MODE
}

